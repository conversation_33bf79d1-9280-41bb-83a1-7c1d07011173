# -*- coding: utf-8 -*-
"""
测试视频引擎修复
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import logger
from src.models.video_engines.video_generation_service import VideoGenerationService
from config.video_generation_config import get_config


async def test_concurrent_video_generation():
    """测试并发视频生成"""
    logger.info("开始测试并发视频生成...")
    
    # 创建服务
    config = get_config('development')
    service = VideoGenerationService(config)
    
    # 创建多个测试任务
    tasks = []
    for i in range(5):  # 测试5个并发任务
        task = asyncio.create_task(
            service.generate_video(
                prompt=f"测试视频 {i+1}",
                image_path="",  # 文生视频
                duration=5.0,
                fps=30,
                width=1024,
                height=1024
            )
        )
        tasks.append(task)
        logger.info(f"创建任务 {i+1}")
    
    # 等待所有任务完成
    logger.info("等待所有任务完成...")
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 检查结果
    success_count = 0
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"任务 {i+1} 异常: {result}")
        else:
            if result.success:
                success_count += 1
                logger.info(f"任务 {i+1} 成功: {result.video_path}")
            else:
                logger.error(f"任务 {i+1} 失败: {result.error_message}")
    
    logger.info(f"测试完成，成功: {success_count}/{len(tasks)}")
    
    # 获取统计信息
    stats = service.manager.get_engine_statistics()
    logger.info(f"引擎统计: {stats}")


if __name__ == "__main__":
    asyncio.run(test_concurrent_video_generation())
