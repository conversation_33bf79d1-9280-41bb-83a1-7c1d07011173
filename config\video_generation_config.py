# -*- coding: utf-8 -*-
"""
视频生成服务配置示例
"""

# 默认配置
DEFAULT_CONFIG = {
    # 输出目录
    'output_dir': 'output/videos',
    
    # 路由策略: 'priority', 'load_balance', 'cost_optimize', 'free_first', 'fastest'
    'routing_strategy': 'free_first',
    
    # 引擎偏好: 'free', 'quality', 'speed', 'local'
    'engine_preferences': ['free', 'quality'],
    
    # 并发限制
    'concurrent_limit': 2,
    
    # 各引擎配置
    'engines': {
        # CogVideoX-Flash (智谱AI免费)
        'cogvideox_flash': {
            'enabled': True,
            'api_key': 'ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY',  # 使用现有的智谱AI API密钥
            'base_url': 'https://open.bigmodel.cn/api/paas/v4',
            'model': 'cogvideox-flash',
            'timeout': 300,  # 5分钟超时
            'max_retries': 3,
            'max_duration': 10.0,  # 最大10秒
            'supported_resolutions': [
                '720x480', '1024x1024', '1280x960', 
                '960x1280', '1920x1080', '1080x1920',
                '2048x1080', '3840x2160'
            ],
            'supported_fps': [30, 60],  # 移除不支持的24fps
            'cost_per_second': 0.0  # 免费
        },
        
        # Replicate Stable Video Diffusion
        'replicate_svd': {
            'enabled': False,
            'api_key': '',  # 需要配置Replicate API密钥
            'base_url': 'https://api.replicate.com/v1',
            'model': 'stability-ai/stable-video-diffusion:3f0457e4619daac51203dedb1a4919c746077d07b83b0d6fcfff9b3b7956a6e',
            'timeout': 600,  # 10分钟超时
            'max_retries': 2,
            'max_duration': 4.0,  # 最大4秒
            'cost_per_second': 0.0125  # 约$0.0125/秒
        },
        
        # PixVerse AI
        'pixverse': {
            'enabled': False,
            'api_key': '',  # 需要配置PixVerse API密钥
            'base_url': 'https://api.pixverse.ai/v1',
            'timeout': 300,
            'max_retries': 2,
            'max_duration': 4.0,
            'cost_per_second': 0.0  # 有免费额度
        },
        
        # Haiper AI
        'haiper': {
            'enabled': False,
            'api_key': '',  # 需要配置Haiper API密钥
            'base_url': 'https://api.haiper.ai/v1',
            'timeout': 300,
            'max_retries': 2,
            'max_duration': 6.0,
            'cost_per_second': 0.0  # 有免费额度
        },
        
        # Runway ML
        'runway_ml': {
            'enabled': False,
            'api_key': '',  # 需要配置Runway API密钥
            'base_url': 'https://api.runwayml.com/v1',
            'timeout': 600,
            'max_retries': 2,
            'max_duration': 16.0,
            'cost_per_second': 0.05  # 约$0.05/秒
        },
        
        # Pika Labs
        'pika_labs': {
            'enabled': False,
            'api_key': '',  # 需要配置Pika API密钥
            'base_url': 'https://api.pika.art/v1',
            'timeout': 300,
            'max_retries': 2,
            'max_duration': 3.0,
            'cost_per_second': 0.02  # 约$0.02/秒
        }
    }
}

# 开发环境配置（主要使用免费服务）
DEVELOPMENT_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'free_first',
    'engine_preferences': ['free'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'cogvideox_flash': {
            **DEFAULT_CONFIG['engines']['cogvideox_flash'],
            'enabled': True
        },
        'replicate_svd': {'enabled': False},
        'runway_ml': {'enabled': False},
        'pika_labs': {'enabled': False}
    }
}

# 生产环境配置（包含付费服务作为备选）
PRODUCTION_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'priority',
    'engine_preferences': ['free', 'quality'],
    'concurrent_limit': 3,
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'cogvideox_flash': {
            **DEFAULT_CONFIG['engines']['cogvideox_flash'],
            'enabled': True,
            'api_key': 'ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY'  # 使用现有的智谱AI API密钥
        },
        'replicate_svd': {
            **DEFAULT_CONFIG['engines']['replicate_svd'],
            'enabled': True,
            'api_key': 'your-replicate-api-key'  # 需要实际配置
        }
    }
}

# 高质量配置（优先使用高质量引擎）
HIGH_QUALITY_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'best_quality',
    'engine_preferences': ['quality', 'free'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'runway_ml': {
            **DEFAULT_CONFIG['engines']['runway_ml'],
            'enabled': True,
            'api_key': 'your-runway-api-key'  # 需要实际配置
        },
        'cogvideox_flash': {
            **DEFAULT_CONFIG['engines']['cogvideox_flash'],
            'enabled': True
        }
    }
}

# 成本优化配置（优先使用免费/低成本引擎）
COST_OPTIMIZED_CONFIG = {
    **DEFAULT_CONFIG,
    'routing_strategy': 'cheapest',
    'engine_preferences': ['free'],
    'engines': {
        **DEFAULT_CONFIG['engines'],
        'cogvideox_flash': {
            **DEFAULT_CONFIG['engines']['cogvideox_flash'],
            'enabled': True
        },
        'pixverse': {
            **DEFAULT_CONFIG['engines']['pixverse'],
            'enabled': True
        },
        'haiper': {
            **DEFAULT_CONFIG['engines']['haiper'],
            'enabled': True
        }
    }
}


def get_config(environment: str = 'development'):
    """根据环境获取配置
    
    Args:
        environment: 环境名称 ('development', 'production', 'high_quality', 'cost_optimized')
    
    Returns:
        配置字典
    """
    configs = {
        'development': DEVELOPMENT_CONFIG,
        'production': PRODUCTION_CONFIG,
        'high_quality': HIGH_QUALITY_CONFIG,
        'cost_optimized': COST_OPTIMIZED_CONFIG,
        'default': DEFAULT_CONFIG
    }
    
    return configs.get(environment, DEFAULT_CONFIG)


def get_engine_config(engine_name: str, environment: str = 'development'):
    """获取特定引擎的配置
    
    Args:
        engine_name: 引擎名称
        environment: 环境名称
    
    Returns:
        引擎配置字典
    """
    config = get_config(environment)
    return config.get('engines', {}).get(engine_name, {})


def is_engine_enabled(engine_name: str, environment: str = 'development') -> bool:
    """检查引擎是否启用
    
    Args:
        engine_name: 引擎名称
        environment: 环境名称
    
    Returns:
        是否启用
    """
    engine_config = get_engine_config(engine_name, environment)
    return engine_config.get('enabled', False)


def get_enabled_engines(environment: str = 'development') -> list:
    """获取启用的引擎列表
    
    Args:
        environment: 环境名称
    
    Returns:
        启用的引擎名称列表
    """
    config = get_config(environment)
    enabled_engines = []
    
    for engine_name, engine_config in config.get('engines', {}).items():
        if engine_config.get('enabled', False):
            enabled_engines.append(engine_name)
    
    return enabled_engines
